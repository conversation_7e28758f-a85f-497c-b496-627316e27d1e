<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Country Selection Modal -->
    <template id="country_selection_modal" name="Country Selection Modal">
        <div class="modal fade" id="countrySelectionModal" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fa fa-globe"></i> Select Your Country
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <i class="fa fa-times" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p class="text-center mb-4">Please select your country to see relevant pricing:</p>
                        
                        <div class="country-grid">
                            <t t-foreach="available_countries" t-as="country">
                                <div class="country-option" t-att-data-country-id="country.id">
                                    <div class="country-card">
                                        <img t-att-src="'/web/image/res.country/%s/image_url' % country.id" 
                                             t-att-alt="country.name" class="country-flag mb-2"/>
                                        <div class="country-name"><t t-esc="country.name"/></div>
                                        <div class="country-currency text-muted">
                                            <t t-esc="country.currency_id.name"/>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="continueWithoutSelection" class="btn btn-link">
                            Continue with default
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- Include Country Selection Modal in Layout -->
    <template id="layout_country_selection" inherit_id="website.layout" name="Country Selection Layout">
        <xpath expr="//footer" position="after">
            <t t-call="ai_amigoattiers.country_selection_modal"/>
        </xpath>
    </template>
</odoo>
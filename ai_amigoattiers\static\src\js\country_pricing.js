odoo.define('website_size_chart_pricing.country_pricing', function (require) {
'use strict';

var publicWidget = require('web.public.widget');
var ajax = require('web.ajax');

publicWidget.registry.CountryPricing = publicWidget.Widget.extend({
    selector: '#countrySelectionModal',
    events: {
        'click .country-option': '_onCountrySelect',
        'click #continueWithoutSelection': '_onContinueWithoutSelection',
        'show.bs.modal': '_onModalShow',
    },

    /**
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);
        this.selectedCountry = null;
        return this._checkAutoShow();
    },

    /**
     * Check if country selection should be shown
     */
    _checkAutoShow: function () {
        // Show country selection on first visit
        if (!localStorage.getItem('country_selection_shown')) {
            // Wait for page to load completely
            setTimeout(function () {
                $('#countrySelectionModal').modal('show');
            }, 1000);
        }
        return Promise.resolve();
    },

    /**
     * Handle country selection
     */
    _onCountrySelect: function (ev) {
        ev.preventDefault();
        var $option = $(ev.currentTarget);
        var countryId = $option.data('country-id');
        
        // Highlight selected country
        this.$('.country-option').removeClass('selected');
        $option.addClass('selected');
        
        // Set selected country
        this.selectedCountry = countryId;
        
        // Set country via AJAX
        this._setCountry(countryId);
        
        // Close modal
        $('#countrySelectionModal').modal('hide');
    },

    /**
     * Set country via AJAX
     */
    _setCountry: function (countryId) {
        var self = this;
        
        return ajax.jsonRpc('/shop/set_country', 'call', {
            'country_id': countryId
        }).then(function (data) {
            if (data.success) {
                // Store in localStorage to prevent showing popup again
                localStorage.setItem('country_selection_shown', 'true');
                
                // Update header display
                self._updateCountryDisplay(data.country);
                
                // Reload page to update prices
                window.location.reload();
            } else if (data.error) {
                console.error('Error setting country:', data.error);
            }
        }).catch(function (error) {
            console.error('Failed to set country:', error);
        });
    },

    /**
     * Update country display in header
     */
    _updateCountryDisplay: function (country) {
        var $display = $('.current-country-display');
        if ($display.length) {
            $display.find('.country-name').text(country.name);
            $display.find('.currency-symbol').text(country.currency_symbol);
        }
    },

    /**
     * Handle continue without selection
     */
    _onContinueWithoutSelection: function (ev) {
        ev.preventDefault();
        this._setCookie('country_popup_shown', 'true', 30); // Remember for 30 days
        this.$el.modal('hide');
    },

    /**
     * Save country selection and update pricing
     */
    _saveCountrySelection: function () {
        if (!this.selectedCountry) {
            return;
        }
        
        var self = this;
        
        // Show loading state
        this._showLoading();
        
        // Save selection via AJAX
        ajax.jsonRpc('/shop/set_country', 'call', {
            'country_code': this.selectedCountry.code,
        }).then(function (result) {
            if (result.success) {
                // Save in cookies
                self._setCookie('selected_country', self.selectedCountry.code, 365);
                self._setCookie('country_popup_shown', 'true', 365);
                
                // Update page pricing
                self._updatePagePricing(result.pricing_data);
                
                // Close modal
                self.$el.modal('hide');
                
                // Show success message
                self._showSuccessMessage();
                
                // Reload page to apply new pricing
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                self._showError(result.error || 'Failed to save country selection');
            }
        }).catch(function (error) {
            console.error('Country selection error:', error);
            self._showError('Failed to save country selection. Please try again.');
        }).finally(function () {
            self._hideLoading();
        });
    },

    /**
     * Update pricing display on current page
     */
    _updatePagePricing: function (pricingData) {
        if (!pricingData) return;
        
        // Update currency symbols
        $('.oe_currency_value').each(function () {
            var $price = $(this);
            var currentPrice = parseFloat($price.text().replace(/[^\d.-]/g, ''));
            
            if (!isNaN(currentPrice) && pricingData.multiplier) {
                var newPrice = currentPrice * pricingData.multiplier;
                $price.text(pricingData.currency_symbol + newPrice.toFixed(2));
            }
        });
        
        // Update currency display in header
        this._updateHeaderCurrency(pricingData);
    },

    /**
     * Update header currency display
     */
    _updateHeaderCurrency: function (pricingData) {
        var $countryDisplay = $('.current-country-display');
        if ($countryDisplay.length) {
            var displayText = pricingData.country_name + ' (' + pricingData.currency_symbol + ')';
            $countryDisplay.find('button').html('<i class="fa fa-globe"></i> ' + displayText);
        }
    },

    /**
     * Show loading state
     */
    _showLoading: function () {
        this.$('.modal-body').append('<div class="loading-overlay"><div class="loading-spinner"></div></div>');
    },

    /**
     * Hide loading state
     */
    _hideLoading: function () {
        this.$('.loading-overlay').remove();
    },

    /**
     * Show success message
     */
    _showSuccessMessage: function () {
        var message = '<div class="alert alert-success text-center">' +
                     '<i class="fa fa-check-circle"></i> Country selection saved! Updating prices...' +
                     '</div>';
        
        this.$('.modal-body').prepend(message);
        
        setTimeout(() => {
            this.$('.alert-success').fadeOut();
        }, 2000);
    },

    /**
     * Show error message
     */
    _showError: function (message) {
        var errorHtml = '<div class="alert alert-error text-center">' +
                       '<i class="fa fa-exclamation-triangle"></i> ' + message +
                       '</div>';
        
        this.$('.modal-body').prepend(errorHtml);
        
        setTimeout(() => {
            this.$('.alert-error').fadeOut();
        }, 3000);
    },

    /**
     * Handle modal show event
     */
    _onModalShow: function () {
        // Load countries when modal is shown
        this._loadCountries();

        // Track analytics if needed
        if (typeof gtag !== 'undefined') {
            gtag('event', 'country_popup_shown', {
                'event_category': 'engagement',
                'event_label': 'country_selection'
            });
        }
    },

    /**
     * Load countries dynamically via AJAX
     */
    _loadCountries: function () {
        var self = this;
        var $countryGrid = this.$('#countryGrid');

        // Show loading state
        $countryGrid.html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading countries...</div>');

        return ajax.jsonRpc('/shop/get_countries', 'call', {}).then(function (data) {
            if (data.success && data.countries) {
                self._renderCountries(data.countries);
            } else {
                $countryGrid.html('<div class="text-center text-danger">Failed to load countries. Please refresh the page.</div>');
                console.error('Failed to load countries:', data.error);
            }
        }).catch(function (error) {
            $countryGrid.html('<div class="text-center text-danger">Failed to load countries. Please refresh the page.</div>');
            console.error('Error loading countries:', error);
        });
    },

    /**
     * Render countries in the grid
     */
    _renderCountries: function (countries) {
        var $countryGrid = this.$('#countryGrid');
        var html = '';

        countries.forEach(function (country) {
            html += '<div class="country-option" data-country-id="' + country.id + '">';
            html += '  <div class="country-card">';

            // Country flag (if available)
            if (country.flag_url) {
                html += '    <img src="' + country.flag_url + '" alt="' + country.name + '" class="country-flag mb-2"/>';
            } else {
                html += '    <div class="country-flag-placeholder mb-2"><i class="fa fa-globe"></i></div>';
            }

            html += '    <div class="country-name">' + country.name + '</div>';
            html += '    <div class="country-currency text-muted">' + country.currency;
            if (country.currency_symbol) {
                html += ' (' + country.currency_symbol + ')';
            }
            html += '</div>';
            html += '  </div>';
            html += '</div>';
        });

        $countryGrid.html(html);
    },

    /**
     * Set cookie
     */
    _setCookie: function (name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    },

    /**
     * Get cookie
     */
    _getCookie: function (name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    },
});

// Country Change Widget (for header dropdown)
publicWidget.registry.CountryChange = publicWidget.Widget.extend({
    selector: '.current-country-display',
    events: {
        'click [data-target="#countrySelectionModal"]': '_onChangeCountryClick',
    },

    /**
     * Handle change country click
     */
    _onChangeCountryClick: function (ev) {
        ev.preventDefault();
        
        // Track analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'change_country_clicked', {
                'event_category': 'engagement',
                'event_label': 'header_country_change'
            });
        }
        
        // Modal will be shown by Bootstrap
    },
});

// Auto-detect country based on IP (optional enhancement)
publicWidget.registry.CountryAutoDetect = publicWidget.Widget.extend({
    selector: 'body',

    /**
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);
        
        // Only auto-detect if no country is selected
        if (!this._getCookie('selected_country')) {
            this._detectCountry();
        }
    },

    /**
     * Detect country based on IP
     */
    _detectCountry: function () {
        // This could use a service like ipapi.co or similar
        // For now, we'll skip auto-detection and rely on manual selection
        console.log('Country auto-detection could be implemented here');
    },

    /**
     * Get cookie helper
     */
    _getCookie: function (name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    },
});

return {
    CountryPricing: publicWidget.registry.CountryPricing,
    CountryChange: publicWidget.registry.CountryChange,
    CountryAutoDetect: publicWidget.registry.CountryAutoDetect,
};

});


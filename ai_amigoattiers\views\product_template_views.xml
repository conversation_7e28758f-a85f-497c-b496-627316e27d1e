<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Product Template Form View Extension -->
    <record id="product_template_form_view_inherit" model="ir.ui.view">
        <field name="name">product.template.form.inherit.size.chart</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='categ_id']" position="after">
                <field name="gender"/>
                <field name="size_chart_id" domain="[('gender', '=', gender)]"/>
            </xpath>
        </field>
    </record>

    <!-- Size Chart Form View -->
    <record id="size_chart_form_view" model="ir.ui.view">
        <field name="name">website.size.chart.form</field>
        <field name="model">website.size.chart</field>
        <field name="arch" type="xml">
            <form string="Size Chart">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="gender"/>
                            <field name="brand_id"/>
                            <field name="category_id"/>
                        </group>
                        <group>
                            <field name="sizing_standard"/>
                            <field name="sequence"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Size Lines">
                            <field name="size_line_ids"/>
                        </page>
                        <page string="Description">
                            <field name="description"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Size Chart Line Tree View -->
    <record id="size_chart_line_tree_view" model="ir.ui.view">
        <field name="name">website.size.chart.line.tree</field>
        <field name="model">website.size.chart.line</field>
        <field name="arch" type="xml">
            <list string="Size Chart Lines" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="size_name"/>
                <field name="size_number"/>
                <field name="chest_cm"/>
                <field name="waist_cm"/>
                <field name="hip_cm"/>
                <field name="shoulder_cm"/>
                <field name="sleeve_cm"/>
                <field name="length_cm"/>
                <field name="neck_cm"/>
                <field name="inseam_cm"/>
            </list>
        </field>
    </record>

    <!-- Size Chart Line Form View -->
    <record id="size_chart_line_form_view" model="ir.ui.view">
        <field name="name">website.size.chart.line.form</field>
        <field name="model">website.size.chart.line</field>
        <field name="arch" type="xml">
            <form string="Size Chart Line">
                <sheet>
                    <group>
                        <group>
                            <field name="size_name"/>
                            <field name="size_number"/>
                            <field name="sequence"/>
                        </group>
                    </group>
                    <group string="Measurements (cm)">
                        <group>
                            <field name="chest_cm"/>
                            <field name="waist_cm"/>
                            <field name="hip_cm"/>
                            <field name="shoulder_cm"/>
                        </group>
                        <group>
                            <field name="sleeve_cm"/>
                            <field name="length_cm"/>
                            <field name="neck_cm"/>
                            <field name="inseam_cm"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Size Chart Tree View -->
    <record id="size_chart_tree_view" model="ir.ui.view">
        <field name="name">website.size.chart.tree</field>
        <field name="model">website.size.chart</field>
        <field name="arch" type="xml">
            <list string="Size Charts">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="gender"/>
                <field name="brand_id"/>
                <field name="sizing_standard"/>
                <field name="active"/>
            </list>
        </field>
    </record>



    <!-- Product Brand Form View -->
    <record id="product_brand_form_view" model="ir.ui.view">
        <field name="name">product.brand.form</field>
        <field name="model">product.brand</field>
        <field name="arch" type="xml">
            <form string="Product Brand">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="logo" widget="image" class="oe_avatar"/>
                        </group>
                    </group>
                    <group>
                        <field name="description"/>
                    </group>
                    <group string="Fitting Notes">
                        <field name="fitting_notes"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Product Brand Tree View -->
    <record id="product_brand_tree_view" model="ir.ui.view">
        <field name="name">product.brand.tree</field>
        <field name="model">product.brand</field>
        <field name="arch" type="xml">
            <list string="Product Brands">
                <field name="name"/>
                <field name="description"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_size_charts" model="ir.actions.act_window">
        <field name="name">Size Charts</field>
        <field name="res_model">website.size.chart</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="size_chart_tree_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first size chart!
            </p>
            <p>
                Size charts help customers choose the right size for your products.
                You can create different charts for different genders and brands.
            </p>
        </field>
    </record>



    <record id="action_product_brands" model="ir.actions.act_window">
        <field name="name">Product Brands</field>
        <field name="res_model">product.brand</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="product_brand_tree_view"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first brand!
            </p>
            <p>
                Brands can have specific fitting notes and size variations.
                This helps customers understand brand-specific sizing.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_size_chart_main" name="Size Charts &amp; Pricing" parent="website.menu_website_configuration" sequence="50"/>

    <menuitem id="menu_size_charts" name="Size Charts" parent="menu_size_chart_main" action="action_size_charts" sequence="10"/>
    <menuitem id="menu_product_brands" name="Product Brands" parent="menu_size_chart_main" action="action_product_brands" sequence="20"/>

</odoo>

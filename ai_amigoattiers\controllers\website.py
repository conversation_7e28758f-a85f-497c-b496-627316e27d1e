# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
import json


class WebsiteSizeChartPricing(http.Controller):

    @http.route('/shop/size_chart', type='json', auth='public', website=True)
    def get_size_chart(self, product_id, **kwargs):
        """Get size chart data for a product"""
        try:
            product_id = int(product_id)
            product = request.env['product.template'].browse(product_id)
            
            if not product.exists():
                return {'error': 'Product not found'}
            
            # Get size chart for the product
            size_chart = product.get_size_chart()
            
            if not size_chart:
                return {'error': 'No size chart available for this product'}
            
            # Format size chart data for frontend
            size_chart_data = {
                'id': size_chart.id,
                'name': size_chart.name,
                'brand_name': size_chart.brand_id.name if size_chart.brand_id else '',
                'sizing_standard': size_chart.sizing_standard,
                'description': size_chart.description,
                'fitting_notes': size_chart.brand_id.fitting_notes if size_chart.brand_id else '',
                'sizes': []
            }
            
            # Add size lines
            for line in size_chart.size_line_ids:
                size_chart_data['sizes'].append({
                    'size_name': line.size_name,
                    'size_number': line.size_number,
                    'measurements': {
                        'chest': {'cm': line.chest_cm, 'inch': line.chest_inch},
                        'waist': {'cm': line.waist_cm, 'inch': line.waist_inch},
                        'hip': {'cm': line.hip_cm, 'inch': line.hip_inch},
                        'shoulder': {'cm': line.shoulder_cm, 'inch': line.shoulder_inch},
                        'sleeve': {'cm': line.sleeve_cm, 'inch': line.sleeve_inch},
                        'length': {'cm': line.length_cm, 'inch': line.length_inch}
                    }
                })
            
            return {'size_chart': size_chart_data}
            
        except Exception as e:
            return {'error': str(e)}

    @http.route('/shop/get_countries', type='json', auth='public', website=True)
    def get_countries(self):
        """Get available countries for selection"""
        try:
            # Use the model method to get all countries with proper currency logic
            countries = request.env['website.country.pricing'].sudo().get_available_countries()
            return {'success': True, 'countries': countries}
        except Exception as e:
            return {'success': False, 'error': str(e), 'countries': []}

    @http.route('/shop/set_country', type='json', auth='public', website=True)
    def set_country(self, country_id):
        """Set selected country in session"""
        try:
            country_id = int(country_id)
            country = request.env['res.country'].browse(country_id)
            
            if not country.exists():
                return {'error': 'Country not found'}
            
            # Set country in session
            request.session['website_sale_current_country'] = country_id
            
            # Get pricing info
            country_pricing = request.env['website.country.pricing'].sudo().search([
                ('country_id', '=', country_id),
                ('active', '=', True)
            ], limit=1)
            
            if country_pricing:
                request.session['website_sale_current_currency'] = country_pricing.currency_id.id
            
            return {
                'success': True,
                'country': {
                    'name': country.name,
                    'currency': country_pricing.currency_id.name if country_pricing else '',
                    'currency_symbol': country_pricing.currency_id.symbol if country_pricing else ''
                }
            }
            
        except Exception as e:
            return {'error': str(e)}

    @http.route('/test/country-popup', type='http', auth='public', website=True)
    def test_country_popup(self):
        """Test page for country popup functionality"""
        return request.render('ai_amigoattiers.test_country_popup')
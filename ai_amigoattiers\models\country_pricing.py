# -*- coding: utf-8 -*-

from odoo import models, fields, api


class Website(models.Model):
    _inherit = 'website'

    @api.model
    def set_country_preference(self, country_code):
        """Set country preference in session"""
        if hasattr(self.env, 'request'):
            self.env.request.session['selected_country'] = country_code

            # Get country and its currency
            country = self.env['res.country'].search([('code', '=', country_code)], limit=1)
            if country and country.currency_id:
                self.env.request.session['currency_id'] = country.currency_id.id

        return True

    def get_selected_country(self):
        """Get currently selected country from session"""
        if hasattr(self.env, 'request'):
            country_code = self.env.request.session.get('selected_country')
            if country_code:
                return self.env['res.country'].search([('code', '=', country_code)], limit=1)
        return False
